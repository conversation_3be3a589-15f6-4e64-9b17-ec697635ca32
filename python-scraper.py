#!/usr/bin/env python3
"""
ANC Program Data Scraper - Python Version
Alternative to Google Apps Script when WAA blocking occurs
"""

import requests
import re
import json
import csv
import time
from urllib.parse import urljoin

# Configuration
BASE_URL = "https://anc.ca.apm.activecommunities.com/ajax/activity/search"
PARAMS = {
    "onlineSiteId": "0",
    "activity_select_param": "2",
    "center_ids": ["3", "5", "36"],
    "activity_other_category_ids": ["7", "2"],
    "viewMode": "list"
}

MAX_PAGES = 5
PAGE_SIZE = 50
SLEEP_SECONDS = 2

def fetch_page(page_num):
    """Fetch a single page of program data"""
    params = PARAMS.copy()
    params.update({
        "page": str(page_num),
        "pageSize": str(PAGE_SIZE),
        "locale": "en-US"
    })
    
    # Handle multiple center_ids and category_ids
    url_params = []
    for key, value in params.items():
        if isinstance(value, list):
            for v in value:
                url_params.append(f"{key}={v}")
        else:
            url_params.append(f"{key}={value}")
    
    url = f"{BASE_URL}?{'&'.join(url_params)}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/html, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://anc.ca.apm.activecommunities.com/'
    }
    
    print(f"Fetching page {page_num}: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"Response status: {response.status_code}")
        print(f"Response length: {len(response.text)}")
        
        return response.text
    
    except requests.RequestException as e:
        print(f"Error fetching page {page_num}: {e}")
        return None

def extract_html_from_response(text):
    """Extract HTML from JSON response if needed"""
    try:
        # Try to parse as JSON first
        data = json.loads(text)
        print("Response is JSON")
        print(f"JSON keys: {list(data.keys())}")
        
        # Look for HTML content in common keys
        html_keys = ["resultsHtml", "html", "activitySearchResultHtml", "content", "viewHtml"]
        for key in html_keys:
            if key in data and isinstance(data[key], str):
                print(f"Found HTML in key: {key}")
                return data[key]
        
        print("No HTML found in JSON response")
        return text
        
    except json.JSONDecodeError:
        print("Response is plain HTML")
        return text

def clean_html(html):
    """Remove scripts and styles from HTML"""
    html = re.sub(r'<script[\s\S]*?</script>', ' ', html, flags=re.IGNORECASE)
    html = re.sub(r'<style[\s\S]*?</style>', ' ', html, flags=re.IGNORECASE)
    return html

def parse_programs(html):
    """Parse program data from HTML"""
    programs = []
    clean = clean_html(html)
    
    print(f"Clean HTML length: {len(clean)}")
    
    # Find program links
    link_pattern = r'<a[^>]+href=["\'](([^"\']*)/activity/search/detail/(\d+)[^"\']*)["\'][^>]*>([\s\S]*?)</a>'
    matches = list(re.finditer(link_pattern, clean, re.IGNORECASE))
    
    print(f"Found {len(matches)} program links")
    
    for i, match in enumerate(matches):
        start_pos = match.start()
        end_pos = matches[i + 1].start() if i + 1 < len(matches) else min(start_pos + 4000, len(clean))
        segment = clean[start_pos:end_pos]
        
        program = {
            'category': '',
            'name': clean_text(match.group(4)),
            'age': extract_age(segment),
            'start_time': extract_time(segment, 'start'),
            'end_time': extract_time(segment, 'end'),
            'fee': extract_fee(segment),
            'description': '',
            'spots': extract_spots(segment),
            'notes': f"URL: https://anc.ca.apm.activecommunities.com{match.group(1)}"
        }
        
        programs.append(program)
    
    return programs

def extract_age(segment):
    """Extract age information from segment"""
    # Age range: "Age at least 8 yrs but less than 14 yrs"
    match = re.search(r'Age[^0-9]*?(\d{1,2})[^0-9]+(?:less than|to|–|-)\s*(\d{1,2})', segment, re.IGNORECASE)
    if match:
        return f"{match.group(1)}–{match.group(2)}"
    
    # Age minimum: "Age 18+"
    match = re.search(r'Age[^0-9]*?(\d{1,2})\s*\+', segment, re.IGNORECASE)
    if match:
        return f"{match.group(1)}+"
    
    return ''

def extract_time(segment, time_type):
    """Extract start or end time from segment"""
    # Pattern: "Wed 6:30 PM - 7:45 PM"
    pattern = r'\b(Mon|Tue|Wed|Thu|Fri|Sat|Sun)[^A-Za-z]{0,12}(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)'
    match = re.search(pattern, segment, re.IGNORECASE)
    
    if not match:
        # Fallback pattern without day
        pattern = r'(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)'
        match = re.search(pattern, segment, re.IGNORECASE)
    
    if match:
        return match.group(-2) if time_type == 'start' else match.group(-1)
    
    return ''

def extract_fee(segment):
    """Extract fee from segment"""
    match = re.search(r'\$\s*([0-9]{1,4}(?:\.[0-9]{2})?)', segment)
    return f"${match.group(1)}" if match else ''

def extract_spots(segment):
    """Extract available spots from segment"""
    match = re.search(r'(Openings|Spots|Spaces)\s*:?[\s]*([0-9]+)', segment, re.IGNORECASE)
    if match:
        return match.group(2)
    
    if re.search(r'Waitlist', segment, re.IGNORECASE):
        return 'Waitlist'
    
    if re.search(r'\bFull\b', segment, re.IGNORECASE):
        return 'Full'
    
    return ''

def clean_text(text):
    """Clean HTML tags and normalize whitespace"""
    text = re.sub(r'<[^>]+>', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def save_to_csv(programs, filename='anc_programs.csv'):
    """Save programs to CSV file"""
    fieldnames = ['category', 'name', 'age', 'start_time', 'end_time', 'fee', 'description', 'spots', 'notes']
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(programs)
    
    print(f"Saved {len(programs)} programs to {filename}")

def main():
    """Main scraping function"""
    all_programs = []
    
    for page in range(1, MAX_PAGES + 1):
        html = fetch_page(page)
        
        if not html:
            print(f"Failed to fetch page {page}, stopping")
            break
        
        html = extract_html_from_response(html)
        programs = parse_programs(html)
        
        if not programs:
            print(f"No programs found on page {page}, stopping")
            break
        
        print(f"Found {len(programs)} programs on page {page}")
        all_programs.extend(programs)
        
        # Sleep between requests
        if page < MAX_PAGES:
            print(f"Sleeping {SLEEP_SECONDS} seconds...")
            time.sleep(SLEEP_SECONDS)
    
    if all_programs:
        save_to_csv(all_programs)
        print(f"\nTotal programs scraped: {len(all_programs)}")
        
        # Print first few programs as sample
        print("\nSample programs:")
        for i, program in enumerate(all_programs[:3]):
            print(f"{i+1}. {program['name']} - {program['age']} - {program['fee']}")
    else:
        print("No programs found!")

if __name__ == "__main__":
    main()
