<!DOCTYPE html>
<html>
<head>
    <title>Program Data Scraper</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        button { padding: 10px 20px; margin: 10px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #3367d6; }
        textarea { width: 100%; height: 200px; margin: 10px 0; font-family: monospace; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ANC Program Data Scraper</h1>
        
        <div class="info">
            <strong>CORS Error Detected - Manual Method Required:</strong>
            <ol>
                <li><strong>Open the ANC website:</strong> <a href="https://anc.ca.apm.activecommunities.com/activity/search" target="_blank">Click here to open ANC search page</a></li>
                <li><strong>Copy page source:</strong> Right-click → "View Page Source" (or Ctrl+U), then select all and copy</li>
                <li><strong>Paste HTML below</strong> and click "Parse HTML Data"</li>
                <li><strong>Copy the CSV data</strong> and paste into your Google Sheet</li>
            </ol>
        </div>

        <button onclick="fetchPrograms()">Try Auto Fetch (may fail due to CORS)</button>
        <button onclick="parseManualHtml()">Parse HTML Data</button>
        <button onclick="clearResults()">Clear Results</button>

        <h3>Paste HTML Source Here:</h3>
        <textarea id="htmlInput" placeholder="Paste the HTML source code from the ANC website here..."></textarea>
        
        <div id="status"></div>
        
        <h3>CSV Data (copy and paste into Google Sheets):</h3>
        <textarea id="csvOutput" placeholder="CSV data will appear here..."></textarea>
        
        <h3>Debug Info:</h3>
        <textarea id="debugOutput" placeholder="Debug information will appear here..."></textarea>
    </div>

    <script>
        const BASE_URL = "https://anc.ca.apm.activecommunities.com/ajax/activity/search?onlineSiteId=0&activity_select_param=2&center_ids=3&center_ids=5&center_ids=36&activity_other_category_ids=7&activity_other_category_ids=2&viewMode=list";
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function debug(message) {
            const debugOutput = document.getElementById('debugOutput');
            debugOutput.value += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        async function fetchPrograms() {
            showStatus('Fetching programs...', 'info');
            debug('Starting program fetch...');
            
            const csvOutput = document.getElementById('csvOutput');
            csvOutput.value = 'Category,Program Name,Age,Start Time,End Time,Fee,Descriptions,Spots Left,Additional Notes\n';
            
            let totalPrograms = 0;
            
            try {
                for (let page = 1; page <= 5; page++) {
                    debug(`Fetching page ${page}...`);
                    
                    const url = `${BASE_URL}&page=${page}&pageSize=50`;
                    debug(`URL: ${url}`);
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json, text/html, */*',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });
                    
                    debug(`Response status: ${response.status}`);
                    
                    if (!response.ok) {
                        debug(`HTTP error: ${response.status} ${response.statusText}`);
                        continue;
                    }
                    
                    const text = await response.text();
                    debug(`Response length: ${text.length}`);
                    
                    let html = text;
                    
                    // Check if response is JSON
                    try {
                        const obj = JSON.parse(text);
                        debug('Response is JSON');
                        debug(`JSON keys: ${Object.keys(obj).join(', ')}`);
                        
                        const keys = ["resultsHtml", "html", "activitySearchResultHtml", "content", "viewHtml"];
                        for (const k of keys) {
                            if (obj && typeof obj[k] === "string") {
                                html = obj[k];
                                debug(`Found HTML in JSON key: ${k}, length: ${html.length}`);
                                break;
                            }
                        }
                    } catch (e) {
                        debug('Response is plain HTML');
                    }
                    
                    const programs = parsePrograms(html);
                    debug(`Found ${programs.length} programs on page ${page}`);
                    
                    if (programs.length === 0) {
                        debug('No programs found, stopping');
                        break;
                    }
                    
                    // Add programs to CSV
                    for (const program of programs) {
                        const csvRow = [
                            escapeCsv(program.category || ''),
                            escapeCsv(program.name || ''),
                            escapeCsv(program.age || ''),
                            escapeCsv(program.startTime || ''),
                            escapeCsv(program.endTime || ''),
                            escapeCsv(program.fee || ''),
                            escapeCsv(program.description || ''),
                            escapeCsv(program.spots || ''),
                            escapeCsv(program.notes || '')
                        ].join(',');
                        
                        csvOutput.value += csvRow + '\n';
                        totalPrograms++;
                    }
                    
                    // Wait between requests
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
                
                showStatus(`Successfully fetched ${totalPrograms} programs!`, 'success');
                debug(`Total programs fetched: ${totalPrograms}`);
                
            } catch (error) {
                showStatus(`Error: ${error.message}`, 'error');
                debug(`Error: ${error.message}`);
            }
        }
        
        function parsePrograms(html) {
            const programs = [];
            
            // Remove scripts and styles
            const clean = html.replace(/<script[\s\S]*?<\/script>/gi, ' ')
                              .replace(/<style[\s\S]*?<\/style>/gi, ' ');
            
            debug(`Clean HTML length: ${clean.length}`);
            
            // Find program links
            const linkRegex = /<a[^>]+href=["']([^"']*\/activity\/search\/detail\/(\d+)[^"']*)["'][^>]*>([\s\S]*?)<\/a>/gi;
            
            let match;
            const anchors = [];
            
            while ((match = linkRegex.exec(clean)) !== null) {
                anchors.push({
                    index: match.index,
                    href: match[1],
                    name: cleanText(match[3])
                });
            }
            
            debug(`Found ${anchors.length} program anchors`);
            
            for (let i = 0; i < anchors.length; i++) {
                const start = anchors[i].index;
                const end = (i + 1 < anchors.length) ? anchors[i + 1].index : Math.min(start + 4000, clean.length);
                const segment = clean.substring(start, end);
                
                const program = {
                    name: anchors[i].name || '',
                    url: anchors[i].href,
                    category: '',
                    age: extractAge(segment),
                    startTime: extractTime(segment, 'start'),
                    endTime: extractTime(segment, 'end'),
                    fee: extractFee(segment),
                    description: '',
                    spots: extractSpots(segment),
                    notes: `URL: https://anc.ca.apm.activecommunities.com${anchors[i].href}`
                };
                
                programs.push(program);
            }
            
            return programs;
        }
        
        function extractAge(segment) {
            const match1 = /Age[^0-9]*?(\d{1,2})[^0-9]+(?:less than|to|–|-)\s*(\d{1,2})/i.exec(segment);
            if (match1) return `${match1[1]}–${match1[2]}`;
            
            const match2 = /Age[^0-9]*?(\d{1,2})\s*\+/i.exec(segment);
            return match2 ? `${match2[1]}+` : '';
        }
        
        function extractTime(segment, type) {
            const timeMatch = /\b(Mon|Tue|Wed|Thu|Fri|Sat|Sun)[^A-Za-z]{0,12}(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)/i.exec(segment) ||
                             /(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)/i.exec(segment);
            
            if (!timeMatch) return '';
            
            return type === 'start' ? timeMatch[timeMatch.length - 2] : timeMatch[timeMatch.length - 1];
        }
        
        function extractFee(segment) {
            const match = /\$\s*([0-9]{1,4}(?:\.[0-9]{2})?)/.exec(segment);
            return match ? `$${match[1]}` : '';
        }
        
        function extractSpots(segment) {
            const match = /(Openings|Spots|Spaces)\s*:?[\s]*([0-9]+)/i.exec(segment);
            if (match) return match[2];
            if (/Waitlist/i.test(segment)) return 'Waitlist';
            if (/\bFull\b/i.test(segment)) return 'Full';
            return '';
        }
        
        function cleanText(text) {
            return text.replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim();
        }
        
        function escapeCsv(text) {
            if (typeof text !== 'string') text = String(text);
            if (text.includes(',') || text.includes('"') || text.includes('\n')) {
                return '"' + text.replace(/"/g, '""') + '"';
            }
            return text;
        }
        
        function parseManualHtml() {
            const htmlInput = document.getElementById('htmlInput');
            const html = htmlInput.value.trim();

            if (!html) {
                showStatus('Please paste HTML source code first', 'error');
                return;
            }

            showStatus('Parsing HTML data...', 'info');
            debug('Starting manual HTML parse...');

            const csvOutput = document.getElementById('csvOutput');
            csvOutput.value = 'Category,Program Name,Age,Start Time,End Time,Fee,Descriptions,Spots Left,Additional Notes\n';

            try {
                const programs = parsePrograms(html);
                debug(`Found ${programs.length} programs in manual HTML`);

                if (programs.length === 0) {
                    showStatus('No programs found in the HTML. Make sure you copied the full page source.', 'error');
                    return;
                }

                // Add programs to CSV
                for (const program of programs) {
                    const csvRow = [
                        escapeCsv(program.category || ''),
                        escapeCsv(program.name || ''),
                        escapeCsv(program.age || ''),
                        escapeCsv(program.startTime || ''),
                        escapeCsv(program.endTime || ''),
                        escapeCsv(program.fee || ''),
                        escapeCsv(program.description || ''),
                        escapeCsv(program.spots || ''),
                        escapeCsv(program.notes || '')
                    ].join(',');

                    csvOutput.value += csvRow + '\n';
                }

                showStatus(`Successfully parsed ${programs.length} programs from HTML!`, 'success');
                debug(`Manual parse complete: ${programs.length} programs`);

            } catch (error) {
                showStatus(`Error parsing HTML: ${error.message}`, 'error');
                debug(`Parse error: ${error.message}`);
            }
        }

        function clearResults() {
            document.getElementById('csvOutput').value = '';
            document.getElementById('debugOutput').value = '';
            document.getElementById('htmlInput').value = '';
            document.getElementById('status').innerHTML = '';
        }
    </script>
</body>
</html>
