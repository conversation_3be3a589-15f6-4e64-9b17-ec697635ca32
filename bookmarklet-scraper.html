<!DOCTYPE html>
<html>
<head>
    <title>ANC Program Scraper - Bookmarklet</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .bookmarklet { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #4285f4; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 10px 0;
            font-weight: bold;
        }
        .bookmarklet:hover { background: #3367d6; }
        .code { 
            background: #f5f5f5; 
            padding: 15px; 
            border-radius: 4px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        .step { 
            background: #e8f4fd; 
            padding: 15px; 
            margin: 10px 0; 
            border-left: 4px solid #4285f4; 
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ANC Program Scraper - Bookmarklet Solution</h1>
        
        <div class="warning">
            <strong>Why use this method?</strong><br>
            Both Google Apps Script and direct browser requests are blocked by CORS policies. 
            A bookmarklet runs directly on the target website, bypassing these restrictions.
        </div>

        <h2>Method 1: Drag & Drop Bookmarklet (Recommended)</h2>
        
        <div class="step">
            <strong>Step 1:</strong> Drag this link to your bookmarks bar:
            <br><br>
            <a href="javascript:(function(){var s=document.createElement('script');s.src='data:text/javascript;base64,'+btoa(`
// ANC Program Scraper Bookmarklet
(function() {
    console.log('ANC Program Scraper starting...');
    
    function extractPrograms() {
        const programs = [];
        const links = document.querySelectorAll('a[href*=\"/activity/search/detail/\"]');
        
        console.log('Found ' + links.length + ' program links');
        
        links.forEach((link, index) => {
            try {
                const container = link.closest('.activity-item, .program-item, .search-result-item') || link.parentElement;
                const text = container ? container.innerText : link.innerText;
                
                const program = {
                    name: link.innerText.trim() || 'Unknown Program',
                    url: link.href,
                    age: extractAge(text),
                    time: extractTime(text),
                    fee: extractFee(text),
                    spots: extractSpots(text),
                    location: extractLocation(text)
                };
                
                programs.push(program);
            } catch (e) {
                console.error('Error processing program ' + index + ':', e);
            }
        });
        
        return programs;
    }
    
    function extractAge(text) {
        const match1 = text.match(/Age[^0-9]*?(\\d{1,2})[^0-9]+(?:less than|to|–|-)\\s*(\\d{1,2})/i);
        if (match1) return match1[1] + '–' + match1[2];
        
        const match2 = text.match(/Age[^0-9]*?(\\d{1,2})\\s*\\+/i);
        return match2 ? match2[1] + '+' : '';
    }
    
    function extractTime(text) {
        const match = text.match(/(\\d{1,2}(?::\\d{2})?\\s*(?:AM|PM)?)\\s*[-–]\\s*(\\d{1,2}(?::\\d{2})?\\s*(?:AM|PM)?)/i);
        return match ? match[1] + ' - ' + match[2] : '';
    }
    
    function extractFee(text) {
        const match = text.match(/\\$\\s*([0-9]{1,4}(?:\\.[0-9]{2})?)/);
        return match ? '$' + match[1] : '';
    }
    
    function extractSpots(text) {
        const match = text.match(/(Openings|Spots|Spaces)\\s*:?[\\s]*([0-9]+)/i);
        if (match) return match[2];
        if (/Waitlist/i.test(text)) return 'Waitlist';
        if (/\\bFull\\b/i.test(text)) return 'Full';
        return '';
    }
    
    function extractLocation(text) {
        const match = text.match(/\\b([A-Z][A-Za-z'&\\-\\s]+(?:Community Centre|Centre|Arena|Complex|Park|School|Library|Facility))\\b/);
        return match ? match[0] : '';
    }
    
    function createCSV(programs) {
        let csv = 'Category,Program Name,Age,Start Time,End Time,Fee,Descriptions,Spots Left,Additional Notes\\n';
        
        programs.forEach(program => {
            const row = [
                '',  // category
                escapeCsv(program.name),
                escapeCsv(program.age),
                escapeCsv(program.time.split(' - ')[0] || ''),
                escapeCsv(program.time.split(' - ')[1] || ''),
                escapeCsv(program.fee),
                '',  // description
                escapeCsv(program.spots),
                escapeCsv('Location: ' + program.location + ' | URL: ' + program.url)
            ].join(',');
            
            csv += row + '\\n';
        });
        
        return csv;
    }
    
    function escapeCsv(text) {
        if (typeof text !== 'string') text = String(text);
        if (text.includes(',') || text.includes('\"') || text.includes('\\n')) {
            return '\"' + text.replace(/\"/g, '\"\"') + '\"';
        }
        return text;
    }
    
    function showResults(programs, csv) {
        const popup = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        popup.document.write(\`
            <html>
            <head><title>ANC Programs - Scraped Data</title></head>
            <body style="font-family: Arial, sans-serif; margin: 20px;">
                <h1>ANC Programs Scraped Successfully!</h1>
                <p><strong>Found \${programs.length} programs</strong></p>
                <h3>CSV Data (copy and paste into Google Sheets):</h3>
                <textarea style="width: 100%; height: 300px; font-family: monospace;">\${csv}</textarea>
                <h3>Program List:</h3>
                <ul>
                    \${programs.map(p => '<li><strong>' + p.name + '</strong> - ' + p.age + ' - ' + p.fee + ' - ' + p.time + '</li>').join('')}
                </ul>
            </body>
            </html>
        \`);
    }
    
    // Main execution
    try {
        const programs = extractPrograms();
        
        if (programs.length === 0) {
            alert('No programs found on this page. Make sure you are on the ANC activity search results page.');
            return;
        }
        
        const csv = createCSV(programs);
        showResults(programs, csv);
        
        console.log('Scraping complete:', programs.length, 'programs found');
        
    } catch (error) {
        console.error('Scraping error:', error);
        alert('Error scraping programs: ' + error.message);
    }
})();
`);document.head.appendChild(s);})();" class="bookmarklet">📊 Scrape ANC Programs</a>
        </div>

        <div class="step">
            <strong>Step 2:</strong> Go to the ANC website: 
            <a href="https://anc.ca.apm.activecommunities.com/activity/search" target="_blank">https://anc.ca.apm.activecommunities.com/activity/search</a>
        </div>

        <div class="step">
            <strong>Step 3:</strong> Click the bookmarklet you just saved to scrape the current page
        </div>

        <div class="step">
            <strong>Step 4:</strong> Copy the CSV data from the popup window and paste it into your Google Sheet
        </div>

        <hr>

        <h2>Method 2: Manual Console Script</h2>
        
        <div class="step">
            <strong>Alternative method if bookmarklet doesn't work:</strong>
            <ol>
                <li>Go to the ANC website</li>
                <li>Press F12 to open Developer Tools</li>
                <li>Click the "Console" tab</li>
                <li>Copy and paste the code below</li>
                <li>Press Enter to run it</li>
            </ol>
        </div>

        <div class="code">// Paste this code in the browser console on the ANC website
(function() {
    console.log('ANC Program Scraper starting...');
    
    function extractPrograms() {
        const programs = [];
        const links = document.querySelectorAll('a[href*="/activity/search/detail/"]');
        
        console.log('Found ' + links.length + ' program links');
        
        links.forEach((link, index) => {
            try {
                const container = link.closest('.activity-item, .program-item, .search-result-item') || link.parentElement;
                const text = container ? container.innerText : link.innerText;
                
                const program = {
                    name: link.innerText.trim() || 'Unknown Program',
                    url: link.href,
                    age: extractAge(text),
                    time: extractTime(text),
                    fee: extractFee(text),
                    spots: extractSpots(text),
                    location: extractLocation(text)
                };
                
                programs.push(program);
            } catch (e) {
                console.error('Error processing program ' + index + ':', e);
            }
        });
        
        return programs;
    }
    
    function extractAge(text) {
        const match1 = text.match(/Age[^0-9]*?(\d{1,2})[^0-9]+(?:less than|to|–|-)\\s*(\d{1,2})/i);
        if (match1) return match1[1] + '–' + match1[2];
        
        const match2 = text.match(/Age[^0-9]*?(\d{1,2})\\s*\\+/i);
        return match2 ? match2[1] + '+' : '';
    }
    
    function extractTime(text) {
        const match = text.match(/(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)/i);
        return match ? match[1] + ' - ' + match[2] : '';
    }
    
    function extractFee(text) {
        const match = text.match(/\$\s*([0-9]{1,4}(?:\.[0-9]{2})?)/);
        return match ? '$' + match[1] : '';
    }
    
    function extractSpots(text) {
        const match = text.match(/(Openings|Spots|Spaces)\s*:?[\s]*([0-9]+)/i);
        if (match) return match[2];
        if (/Waitlist/i.test(text)) return 'Waitlist';
        if (/\bFull\b/i.test(text)) return 'Full';
        return '';
    }
    
    function extractLocation(text) {
        const match = text.match(/\b([A-Z][A-Za-z'&\-\s]+(?:Community Centre|Centre|Arena|Complex|Park|School|Library|Facility))\b/);
        return match ? match[0] : '';
    }
    
    function createCSV(programs) {
        let csv = 'Category,Program Name,Age,Start Time,End Time,Fee,Descriptions,Spots Left,Additional Notes\n';
        
        programs.forEach(program => {
            const row = [
                '',  // category
                escapeCsv(program.name),
                escapeCsv(program.age),
                escapeCsv(program.time.split(' - ')[0] || ''),
                escapeCsv(program.time.split(' - ')[1] || ''),
                escapeCsv(program.fee),
                '',  // description
                escapeCsv(program.spots),
                escapeCsv('Location: ' + program.location + ' | URL: ' + program.url)
            ].join(',');
            
            csv += row + '\n';
        });
        
        return csv;
    }
    
    function escapeCsv(text) {
        if (typeof text !== 'string') text = String(text);
        if (text.includes(',') || text.includes('"') || text.includes('\n')) {
            return '"' + text.replace(/"/g, '""') + '"';
        }
        return text;
    }
    
    // Main execution
    try {
        const programs = extractPrograms();
        
        if (programs.length === 0) {
            console.log('No programs found on this page.');
            return;
        }
        
        const csv = createCSV(programs);
        
        console.log('=== SCRAPING COMPLETE ===');
        console.log('Found ' + programs.length + ' programs');
        console.log('CSV data (copy this):');
        console.log(csv);
        
        // Also try to copy to clipboard if possible
        if (navigator.clipboard) {
            navigator.clipboard.writeText(csv).then(() => {
                console.log('CSV data copied to clipboard!');
            }).catch(() => {
                console.log('Could not copy to clipboard, please copy manually from above');
            });
        }
        
    } catch (error) {
        console.error('Scraping error:', error);
    }
})();</div>

        <h2>Troubleshooting</h2>
        <ul>
            <li><strong>No programs found:</strong> Make sure you're on the search results page with program listings visible</li>
            <li><strong>Bookmarklet not working:</strong> Try the console method instead</li>
            <li><strong>Partial data:</strong> The website might load content dynamically - try scrolling down first</li>
            <li><strong>Browser blocking:</strong> Some browsers block bookmarklets - try disabling popup blockers</li>
        </ul>
    </div>
</body>
</html>
