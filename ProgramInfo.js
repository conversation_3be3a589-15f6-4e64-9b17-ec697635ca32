/***** CONFIG: your LIST page (the one in your screenshot) *****/
const BASE_LIST_URL =
  "https://anc.ca.apm.activecommunities.com/ajax/activity/search?onlineSiteId=0&activity_select_param=2&center_ids=3&center_ids=5&center_ids=36&activity_other_category_ids=7&activity_other_category_ids=2&viewMode=list";

const MAX_PAGES  = 10;      // how many &page=… pages to try
const PAGE_SIZE  = 100;     // many ActiveNet endpoints support pageSize
const SLEEP_MS   = 1000;     // polite delay
const USER_AGENT = "SheetsProgramScraper/3.0 (+<EMAIL>)";

/***** SHEET COLUMNS (1-indexed) *****/
const COL = {
  CATEGORY: 1,
  NAME: 2,
  AGE: 3,
  START: 4,
  END: 5,
  FEE: 6,
  DESC: 7,
  SPOTS: 8,
  NOTES: 9
};
const HEADER_ROW = 1;

/***** MENU *****/
function onOpen() {
  SpreadsheetApp.getUi()
    .createMenu("Programs")
    .addItem("Fetch from LIST view", "fetchFromListOnly")
    .addItem("Debug HTML Response", "debugHtmlResponse")
    .addToUi();
}

/***** DEBUG: Check what HTML we're getting *****/
function debugHtmlResponse() {
  const url = addParams(BASE_LIST_URL, { page: "1", pageSize: String(PAGE_SIZE), locale: "en-US" });
  console.log(`Debug URL: ${url}`);

  const { html, origin } = fetchListHtml(url);
  console.log(`HTML length: ${html ? html.length : 0}`);
  console.log(`Origin: ${origin}`);

  if (html) {
    console.log(`First 1000 chars: ${html.substring(0, 1000)}`);
    console.log(`Last 1000 chars: ${html.substring(Math.max(0, html.length - 1000))}`);

    // Check for common patterns
    const hasActivityLinks = /\/activity\/search\/detail\/\d+/i.test(html);
    const hasAnchors = /<a[^>]+href/i.test(html);
    console.log(`Has activity links: ${hasActivityLinks}`);
    console.log(`Has anchor tags: ${hasAnchors}`);
  }

  SpreadsheetApp.getUi().alert(`Debug complete. Check console logs.`);
}

/***** ENTRY: parse ONLY the list view (no detail clicks) *****/
function fetchFromListOnly() {
  const sh = SpreadsheetApp.getActiveSheet();
  ensureHeaders(sh);

  const seenUrls = new Set(getExistingUrls(sh));
  let row = Math.max(sh.getLastRow() + 1, HEADER_ROW + 1);
  let total = 0;

  for (let page = 1; page <= MAX_PAGES; page++) {
    console.log(`Fetching page ${page}...`);
    const url = addParams(BASE_LIST_URL, { page: String(page), pageSize: String(PAGE_SIZE), locale: "en-US" });
    console.log(`URL: ${url}`);

    const { html, origin } = fetchListHtml(url);
    console.log(`HTML length: ${html ? html.length : 0}`);
    if (!html) {
      console.log(`No HTML returned for page ${page}, breaking`);
      break;
    }

    const cards = parseListCards(html, origin);
    console.log(`Found ${cards.length} cards on page ${page}`);
    if (!cards.length) {
      console.log(`No cards found on page ${page}, breaking`);
      break;
    }

    for (const c of cards) {
      if (seenUrls.has(c.url)) continue;
      sh.getRange(row, COL.CATEGORY).setValue(""); // category isn’t prominent on list cards
      sh.getRange(row, COL.NAME).setValue(c.name || "");
      sh.getRange(row, COL.AGE).setValue(c.ageText || "");
      sh.getRange(row, COL.START).setValue(c.startTime || "");
      sh.getRange(row, COL.END).setValue(c.endTime || "");
      sh.getRange(row, COL.FEE).setValue(c.feeCad ?? "");
      sh.getRange(row, COL.DESC).setValue(""); // list view has tiny blurbs; we leave blank (detail page has more)
      sh.getRange(row, COL.SPOTS).setValue(c.spotsLeft ?? "");
      sh.getRange(row, COL.NOTES).setValue(
        [c.location ? ("Location: " + c.location) : "", c.dates ? ("Dates: " + c.dates) : "", "URL: " + c.url]
          .filter(Boolean).join(" | ")
      );
      seenUrls.add(c.url);
      row++; total++;
    }

    Utilities.sleep(SLEEP_MS);
  }

  SpreadsheetApp.getUi().alert(`Imported ${total} programs from list view.`);
}

/***** Parse list HTML into cards *****/
function parseListCards(html, origin) {
  const clean = stripScriptsStyles(html);
  console.log(`Clean HTML length: ${clean.length}`);
  console.log(`First 500 chars of clean HTML: ${clean.substring(0, 500)}`);

  // Find each program anchor to build a segment per card
  // Example links: /activity/search/detail/94426 …
  const re = /<a[^>]+href=["']([^"']*\/activity\/search\/detail\/(\d+)[^"']*)["'][^>]*>([\s\S]*?)<\/a>/ig;

  // Debug: Let's also try to find any activity links at all
  const activityLinks = clean.match(/\/activity\/search\/detail\/\d+/g) || [];
  console.log(`Found ${activityLinks.length} activity detail links in HTML`);
  if (activityLinks.length > 0) {
    console.log(`First few activity links: ${activityLinks.slice(0, 3).join(', ')}`);
  }

  // Debug: Check for any anchor tags
  const allAnchors = clean.match(/<a[^>]+href=["'][^"']*["'][^>]*>/g) || [];
  console.log(`Found ${allAnchors.length} total anchor tags`);
  if (allAnchors.length > 0) {
    console.log(`First anchor: ${allAnchors[0]}`);
  }

  const items = [];
  let m, anchors = [];
  while ((m = re.exec(clean)) !== null) {
    anchors.push({ index: m.index, href: toAbsoluteUrl(origin, m[1]), name: cleanText(m[3]) });
  }
  console.log(`Found ${anchors.length} anchor matches with regex`);
  for (let i = 0; i < anchors.length; i++) {
    const start = anchors[i].index;
    const end = (i + 1 < anchors.length) ? anchors[i + 1].index : Math.min(start + 4000, clean.length);
    const segment = clean.substring(start, end);

    const name = anchors[i].name || "";
    const url  = anchors[i].href;

    // Age text e.g. "Age at least 8 yrs but less than 14 yrs" → keep as text “8–14”
    const ageText = (() => {
      const mm = /Age[^0-9]*?(\d{1,2})[^0-9]+(?:less than|to|–|-)\s*(\d{1,2})/i.exec(segment);
      if (mm) return `${mm[1]}–${mm[2]}`;
      const mm2 = /Age[^0-9]*?(\d{1,2})\s*\+/i.exec(segment);
      return mm2 ? `${mm2[1]}+` : "";
    })();

    // Dates line e.g. "September 24, 2025 to November 12, 2025"
    const dates = (() => {
      const mm = /([A-Z][a-z]+ \d{1,2}, \d{4})\s+to\s+([A-Z][a-z]+ \d{1,2}, \d{4})/i.exec(segment);
      return mm ? `${mm[1]} to ${mm[2]}` : "";
    })();

    // Day/time e.g. "Wed 6:30 PM - 7:45 PM"
    const timeMatch =
      /\b(Mon|Tue|Wed|Thu|Fri|Sat|Sun)[^A-Za-z]{0,12}(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)/i.exec(segment) ||
      /(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)\s*[-–]\s*(\d{1,2}(?::\d{2})?\s*(?:AM|PM)?)/i.exec(segment);
    const startTime = timeMatch ? timeMatch[timeMatch.length - 2] : "";
    const endTime   = timeMatch ? timeMatch[timeMatch.length - 1] : "";

    // Fee e.g. "$183.00"
    const fee = (() => {
      const mm = /\$\s*([0-9]{1,4}(?:\.[0-9]{2})?)/.exec(segment);
      return mm ? Number(mm[1]) : "";
    })();

    // Location line (near the pin icon): capture common facility names
    const location = (() => {
      const mm = /\b([A-Z][A-Za-z'&\-\s]+(?:Community Centre|Centre|Arena|Complex|Park|School|Library|Facility))\b/.exec(segment);
      return mm ? mm[0] : "";
    })();

    // Spots / openings
    const spotsLeft = (() => {
      const mm = /(Openings|Spots|Spaces)\s*:?[\s]*([0-9]+)/i.exec(segment);
      if (mm) return Number(mm[2]);
      if (/Waitlist/i.test(segment)) return "Waitlist";
      if (/\bFull\b/i.test(segment)) return "Full";
      return "";
    })();

    items.push({ name, url, ageText, dates, startTime, endTime, feeCad: fee, location, spotsLeft });
  }

  return items;
}

/***** List-page fetch that may return JSON-with-HTML *****/
function fetchListHtml(url) {
  const resp = safeFetch(url);
  if (!resp) {
    console.log("safeFetch returned null");
    return { html: "", origin: getOrigin(url) };
  }

  console.log(`Response code: ${resp.getResponseCode()}`);
  const text = resp.getContentText();
  console.log(`Response text length: ${text.length}`);
  console.log(`Response starts with: ${text.substring(0, 200)}`);

  let html = text;
  let isJson = false;

  // Some endpoints return JSON with an HTML snippet inside
  try {
    const obj = JSON.parse(text);
    isJson = true;
    console.log("Response is JSON, looking for HTML content...");
    console.log(`JSON keys: ${Object.keys(obj).join(', ')}`);

    const keys = ["resultsHtml", "html", "activitySearchResultHtml", "content", "viewHtml"];
    for (const k of keys) {
      if (obj && typeof obj[k] === "string") {
        html = obj[k];
        console.log(`Found HTML in JSON key: ${k}, length: ${html.length}`);
        break;
      }
    }
    if (html === text) {
      console.log("No HTML found in JSON response");
    }
  } catch (_) {
    console.log("Response is plain HTML, not JSON");
  }

  return { html, origin: getOrigin(url) };
}

/***** HTTP helpers *****/
function safeFetch(url) {
  try {
    console.log(`Fetching URL: ${url}`);
    const response = UrlFetchApp.fetch(url, {
      method: "get",
      followRedirects: true,
      muteHttpExceptions: true,
      validateHttpsCertificates: true,
      headers: {
        "User-Agent": USER_AGENT,
        "Accept": "text/html,application/json",
        "X-Requested-With": "XMLHttpRequest"
      }
    });
    console.log(`Fetch successful, response code: ${response.getResponseCode()}`);
    return response;
  } catch (e) {
    console.log(`Fetch error: ${e.toString()}`);
    return null;
  }
}

/***** Sheet helpers *****/
function ensureHeaders(sh) {
  const headers = ["Category","Program Name","Age","Start Time","End Time","Fee","Descriptions","Spots Left","Additional Notes"];
  sh.getRange(HEADER_ROW, 1, 1, headers.length).setValues([headers]);
}
function getExistingUrls(sh) {
  const last = sh.getLastRow();
  if (last <= HEADER_ROW) return [];
  return sh.getRange(HEADER_ROW + 1, COL.NOTES, last - HEADER_ROW, 1)
           .getValues().flat().map(v => {
             const m = /URL:\s*(\S+)/i.exec(String(v||""));
             return m ? m[1] : "";
           }).filter(Boolean);
}

/***** Small utilities *****/
function addParams(url, params) {
  try {
    const u = new URL(url);
    Object.entries(params).forEach(([k, v]) => u.searchParams.set(k, v));
    return u.toString();
  } catch (_) {
    const sp = Object.entries(params).map(([k,v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`).join("&");
    return url + (url.includes("?") ? "&" : "?") + sp;
  }
}
function stripScriptsStyles(s) {
  return String(s || "")
    .replace(/<script[\s\S]*?<\/script>/gi, " ")
    .replace(/<style[\s\S]*?<\/style>/gi, " ");
}
function cleanText(s) {
  return stripScriptsStyles(s).replace(/<[^>]+>/g, " ").replace(/\s+/g, " ").trim();
}
function getOrigin(u) { try { const x = new URL(u); return `${x.protocol}//${x.host}`; } catch { return ""; } }
function toAbsoluteUrl(origin, href) { try { return new URL(href, origin).toString(); } catch { return href; } }
